import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type * as schema from '../../../src/database/schema.js';

// Mock the drizzle migrate function
const mockDrizzleMigrate = vi.fn();

vi.mock('drizzle-orm/postgres-js/migrator', () => ({
  migrate: mockDrizzleMigrate,
}));

// Mock postgres
const mockPostgres = vi.fn();
vi.mock('postgres', () => ({
  default: mockPostgres,
}));

// Mock drizzle
const mockDrizzle = vi.fn();
vi.mock('drizzle-orm/postgres-js', () => ({
  drizzle: mockDrizzle,
}));

// Mock dotenv
const mockConfig = vi.fn();
vi.mock('dotenv', () => ({
  config: mockConfig,
}));

// Mock fs
const mockExistsSync = vi.fn();
vi.mock('node:fs', () => ({
  existsSync: mockExistsSync,
}));

// Mock path
const mockResolve = vi.fn();
const mockDirname = vi.fn();
vi.mock('node:path', () => ({
  resolve: mockResolve,
  dirname: mockDirname,
}));

// Mock url
const mockFileURLToPath = vi.fn();
vi.mock('node:url', () => ({
  fileURLToPath: mockFileURLToPath,
}));

describe('Database Migration Utility', () => {
  let mockDb: PostgresJsDatabase<typeof schema>;
  let mockClient: unknown;
  let originalEnv: NodeJS.ProcessEnv;
  let originalArgv: string[];

  beforeEach(() => {
    // Save original environment and argv
    originalEnv = { ...process.env };
    originalArgv = [...process.argv];

    // Set required environment variables for tests
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET =
      'test-jwt-secret-that-is-at-least-32-characters-long';

    // Reset all mocks
    vi.clearAllMocks();

    // Create mock database instance
    mockDb = {
      query: vi.fn(),
      select: vi.fn(),
      insert: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    } as unknown as PostgresJsDatabase<typeof schema>;

    // Create mock client
    mockClient = {
      end: vi.fn(),
    };

    // Setup default mock implementations
    mockFileURLToPath.mockReturnValue('/path/to/migrate.js');
    mockDirname.mockReturnValue('/path/to');
    mockResolve.mockImplementation((...args) => args.join('/'));
    mockPostgres.mockReturnValue(mockClient);
    mockDrizzle.mockReturnValue(mockDb);
    mockExistsSync.mockReturnValue(true);
  });

  afterEach(() => {
    // Restore original environment and argv
    process.env = originalEnv;
    process.argv = originalArgv;
    vi.restoreAllMocks();
  });

  describe('migrate function', () => {
    it('should call drizzle migrate with correct parameters', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };

      // Mock successful migration
      mockDrizzleMigrate.mockResolvedValue(undefined);

      await migrate(mockDb, options);

      expect(mockDrizzleMigrate).toHaveBeenCalledWith(mockDb, options);
      expect(mockDrizzleMigrate).toHaveBeenCalledTimes(1);
    });

    it('should return the result from drizzle migrate', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };
      const expectedResult = { success: true };

      mockDrizzleMigrate.mockResolvedValue(expectedResult);

      const result = await migrate(mockDb, options);

      expect(result).toBe(expectedResult);
    });

    it('should propagate errors from drizzle migrate', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };
      const error = new Error('Migration failed');

      mockDrizzleMigrate.mockRejectedValue(error);

      await expect(migrate(mockDb, options)).rejects.toThrow(
        'Migration failed'
      );
    });

    it('should handle empty migrations folder', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '' };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      await migrate(mockDb, options);

      expect(mockDrizzleMigrate).toHaveBeenCalledWith(mockDb, options);
    });

    it('should handle migrations folder with special characters', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/with spaces/migrations-2024' };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      await migrate(mockDb, options);

      expect(mockDrizzleMigrate).toHaveBeenCalledWith(mockDb, options);
    });
  });

  describe('Environment Variable Handling', () => {
    it('should not load .env file when DATABASE_URL is already set', async () => {
      // Set DATABASE_URL in environment
      process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';

      // Re-import the module to trigger environment setup
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      // Should not call config since DATABASE_URL is already set
      expect(mockConfig).not.toHaveBeenCalled();
    });

    it('should load .env.test file in test environment', async () => {
      // Clear DATABASE_URL and set test environment
      process.env.DATABASE_URL = undefined;
      process.env.NODE_ENV = 'test';

      mockExistsSync.mockReturnValue(true);
      mockResolve.mockReturnValue('/path/to/.env.test');

      // Re-import the module to trigger environment setup
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      expect(mockConfig).toHaveBeenCalledWith({ path: '/path/to/.env.test' });
    });

    it('should load .env file in development environment', async () => {
      // Clear DATABASE_URL and set development environment
      process.env.DATABASE_URL = undefined;
      process.env.NODE_ENV = 'development';

      mockExistsSync.mockReturnValue(true);
      mockResolve.mockReturnValue('/path/to/.env');

      // Re-import the module to trigger environment setup
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      expect(mockConfig).toHaveBeenCalledWith({ path: '/path/to/.env' });
    });

    it('should default to development environment when NODE_ENV is not set', async () => {
      // Clear environment variables
      process.env.DATABASE_URL = undefined;
      process.env.NODE_ENV = undefined;

      mockExistsSync.mockReturnValue(true);
      mockResolve.mockReturnValue('/path/to/.env');

      // Re-import the module to trigger environment setup
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      expect(mockConfig).toHaveBeenCalledWith({ path: '/path/to/.env' });
    });

    it('should not load config when .env file does not exist', async () => {
      // Clear DATABASE_URL
      process.env.DATABASE_URL = undefined;
      process.env.NODE_ENV = 'development';

      mockExistsSync.mockReturnValue(false);

      // Re-import the module to trigger environment setup
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      expect(mockConfig).not.toHaveBeenCalled();
    });
  });

  describe('File System Operations', () => {
    it('should resolve correct paths for migrations folder', async () => {
      mockFileURLToPath.mockReturnValue('/app/src/database/migrate.js');
      mockDirname.mockReturnValue('/app/src/database');
      mockResolve.mockImplementation((...args) => args.join('/'));

      // Re-import to trigger path resolution
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      expect(mockFileURLToPath).toHaveBeenCalled();
      expect(mockDirname).toHaveBeenCalled();
    });

    it('should handle path resolution errors gracefully', async () => {
      mockFileURLToPath.mockImplementation(() => {
        throw new Error('Path resolution failed');
      });

      // Should not throw during import
      await expect(async () => {
        vi.resetModules();
        await import('../../../src/database/migrate.js');
      }).not.toThrow();
    });
  });

  describe('migrate function validation', () => {
    it('should accept valid database instance', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      // Should not throw with valid database instance
      await expect(migrate(mockDb, options)).resolves.not.toThrow();
    });

    it('should accept valid options object', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/valid/path' };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      await expect(migrate(mockDb, options)).resolves.not.toThrow();
    });

    it('should handle database connection errors', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };
      const connectionError = new Error('Database connection failed');

      mockDrizzleMigrate.mockRejectedValue(connectionError);

      await expect(migrate(mockDb, options)).rejects.toThrow(
        'Database connection failed'
      );
    });

    it('should handle invalid migration files', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };
      const migrationError = new Error('Invalid migration file format');

      mockDrizzleMigrate.mockRejectedValue(migrationError);

      await expect(migrate(mockDb, options)).rejects.toThrow(
        'Invalid migration file format'
      );
    });
  });

  describe('migrate function edge cases', () => {
    it('should handle null database instance gracefully', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };
      const nullError = new Error('Database instance is null');

      mockDrizzleMigrate.mockRejectedValue(nullError);

      await expect(
        migrate(null as unknown as PostgresJsDatabase<typeof schema>, options)
      ).rejects.toThrow();
    });

    it('should handle undefined options gracefully', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const undefinedError = new Error('Options are undefined');

      mockDrizzleMigrate.mockRejectedValue(undefinedError);

      await expect(
        migrate(mockDb, undefined as unknown as { migrationsFolder: string })
      ).rejects.toThrow();
    });

    it('should handle concurrent migration attempts', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const options = { migrationsFolder: '/path/to/migrations' };

      // Mock a delay to simulate concurrent calls
      mockDrizzleMigrate.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(undefined), 100))
      );

      // Start multiple migrations concurrently
      const migration1 = migrate(mockDb, options);
      const migration2 = migrate(mockDb, options);

      await Promise.all([migration1, migration2]);

      // Both should complete successfully
      expect(mockDrizzleMigrate).toHaveBeenCalledTimes(2);
    });

    it('should handle very long migration folder paths', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const longPath =
        '/very/long/path/to/migrations/folder/with/many/nested/directories/that/might/cause/issues';
      const options = { migrationsFolder: longPath };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      await migrate(mockDb, options);

      expect(mockDrizzleMigrate).toHaveBeenCalledWith(mockDb, options);
    });

    it('should handle special characters in migration folder paths', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const specialPath = '/path/with-special_chars/migrations@2024/folder#1';
      const options = { migrationsFolder: specialPath };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      await migrate(mockDb, options);

      expect(mockDrizzleMigrate).toHaveBeenCalledWith(mockDb, options);
    });

    it('should handle unicode characters in migration folder paths', async () => {
      const { migrate } = await import('../../../src/database/migrate.js');
      const unicodePath = '/path/with/unicode/字符/migrations/フォルダ';
      const options = { migrationsFolder: unicodePath };

      mockDrizzleMigrate.mockResolvedValue(undefined);

      await migrate(mockDb, options);

      expect(mockDrizzleMigrate).toHaveBeenCalledWith(mockDb, options);
    });
  });

  describe('Database Connection Handling', () => {
    it('should create postgres client with correct URL', async () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';

      // Mock the main function execution path
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const processExitSpy = vi
        .spyOn(process, 'exit')
        .mockImplementation(() => {
          throw new Error('process.exit called');
        });

      mockDrizzleMigrate.mockResolvedValue(undefined);

      try {
        // Re-import and trigger main execution
        vi.resetModules();

        // Set argv to simulate direct execution
        process.argv[1] = '/path/to/migrate.js';

        // Import with meta.url check
        await import('../../../src/database/migrate.js');
      } catch (error) {
        // Expected due to process.exit mock
      }

      consoleSpy.mockRestore();
      processExitSpy.mockRestore();
    });

    it('should handle missing DATABASE_URL in main function', async () => {
      process.env.DATABASE_URL = undefined;

      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const processExitSpy = vi
        .spyOn(process, 'exit')
        .mockImplementation(() => {
          throw new Error('process.exit called');
        });

      try {
        // Re-import and trigger main execution
        vi.resetModules();

        // Set argv to simulate direct execution
        process.argv[1] = '/path/to/migrate.js';

        await import('../../../src/database/migrate.js');
      } catch (error) {
        // Expected due to process.exit mock
      }

      consoleErrorSpy.mockRestore();
      processExitSpy.mockRestore();
    });

    it('should handle database connection errors in main function', async () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const processExitSpy = vi
        .spyOn(process, 'exit')
        .mockImplementation(() => {
          throw new Error('process.exit called');
        });

      mockDrizzleMigrate.mockRejectedValue(new Error('Connection failed'));

      try {
        // Re-import and trigger main execution
        vi.resetModules();

        // Set argv to simulate direct execution
        process.argv[1] = '/path/to/migrate.js';

        await import('../../../src/database/migrate.js');
      } catch (error) {
        // Expected due to process.exit mock
      }

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
      processExitSpy.mockRestore();
    });
  });

  describe('Module Import Behavior', () => {
    it('should not execute main when imported as module', async () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Set argv to simulate module import (not direct execution)
      process.argv[1] = '/different/path/script.js';

      // Re-import the module
      vi.resetModules();
      await import('../../../src/database/migrate.js');

      // Main should not execute when imported as module
      expect(consoleSpy).not.toHaveBeenCalledWith('Running migrations...');

      consoleSpy.mockRestore();
    });

    it('should export migrate function when imported', async () => {
      const module = await import('../../../src/database/migrate.js');

      expect(module.migrate).toBeDefined();
      expect(typeof module.migrate).toBe('function');
    });
  });
});
