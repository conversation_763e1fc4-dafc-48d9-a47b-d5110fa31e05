# System Patterns: Bud<PERSON><PERSON>

## System Architecture
BudApp uses client-server architecture with Flutter mobile app and Node.js GraphQL API, PostgreSQL database via Supabase, JWT authentication with OAuth 2.0, and GraphQL client caching for offline support.

## Key Technical Decisions
- **Monorepo Structure**: Turborepo with pnpm workspaces, simplified apps-only structure (packages relocated to apps)
- **GraphQL Schema**: Code-first approach with API-generated schema consumed by mobile app
- **Double-Entry Accounting**: Core financial transactions use double-entry principles for data integrity
- **State Management**: Riverpod for Flutter state management
- **Database ORM**: Drizzle ORM for type-safe database interactions
- **Authentication**: Custom JWT + OAuth 2.0 with secure token storage and comprehensive error handling
- **Testing Framework**: Vitest with transaction-based isolation, 543/543 tests passing (100% success)

## Component Architecture

### Backend Components
- **GraphQL API**: Fastify 5 + Mercurius serving queries/mutations
- **Database Module** (`apps/api/src/database`): Schema, migrations, seeding with double-entry accounting
- **Authentication System**: JWT generation/verification, OAuth flows, password hashing
- **Service Layer**: Modular services with Zod validation and TypeScript types
  - **Accounts Service**: Complete CRUD operations with authentication and soft delete
  - **Auth Service**: User registration, login, token management
- **GraphQL Schema**: Code-first approach with modular type definitions
  - **Account Types**: Account, AccountType enum, CreateAccountInput, UpdateAccountInput
  - **Auth Types**: User, AuthPayload, RegisterInput, LoginInput
- **Error Handling**: Standardized error types with user-friendly messages
- **Testing Infrastructure**: 344 unit tests, 178 integration tests, 41+ E2E tests

### Mobile Components  
- **Navigation System**: Shell routes with persistent bottom navigation using go_router
- **Screen Implementation**: Complete screens (Home, Accounts, Categories, Reports, Profile) with Material 3 UI
- **State Management**: Riverpod providers for authentication and app state
- **GraphQL Client**: Data fetching with authentication headers and offline caching
- **Secure Storage**: flutter_secure_storage for authentication tokens
- **Account Management**: Static UI ready for API integration with real account data
- **Design System Excellence**: Centralized design tokens with consistent theming
  - **Design Tokens**: `design_tokens.dart` as single source of truth for colors, spacing, typography
  - **Theme Integration**: `app_theme.dart` consumes all values from design tokens (no hardcoded values)
  - **Semantic Colors**: Comprehensive color system with proper accessibility support
  - **Component Consistency**: All UI components use centralized design tokens
- **Environment Configuration**: Flexible API endpoint configuration for different deployment scenarios
  - **Environment Class**: `lib/config/environment.dart` with compile-time configuration using `--dart-define`
  - **Build Scripts**: 4 environment-specific build scripts (dev, local, staging, production)
  - **Team Collaboration**: Different developers can use different local network configurations
  - **CI/CD Ready**: Build scripts work seamlessly with automated deployment
  - **Security**: No sensitive configuration in source control
- **Mobile UI Color Scheme**: Beautiful branded design system with Material Design 3 compliance
  - **Primary Teal**: `#01A2A1` used throughout navigation, buttons, and key UI elements
  - **Accent Coral Red**: `#FC2F20` for secondary actions and highlights
  - **Financial Semantics**: Success (green), error (red), warning (orange), info (blue) for financial indicators
  - **Splash Screen**: Beautiful gradient background with elevated app icon design
  - **Component Theming**: Complete theme system for buttons, cards, inputs, navigation
  - **Platform Integration**: Android native splash screen colors for consistent branding
  - **Accessibility**: Proper contrast ratios and semantic color usage

## Critical Implementation Paths

### Accounts Management Flow (COMPLETED BACKEND)
1. **Database Schema**: Complete accounts table with all required fields and relationships
2. **Service Layer**: Zod validation, TypeScript types, authentication checks
3. **GraphQL Schema**: Account type, AccountType enum, input types for create/update
4. **Resolvers**: Full CRUD operations (accounts, account, createAccount, updateAccount, deleteAccount)
5. **Authentication**: User authorization for all account operations
6. **Soft Delete**: Archive functionality instead of hard deletion for data integrity

### Accounts Management Flow (MOBILE UI - IN PROGRESS)
1. **GraphQL Operations**: Create account queries/mutations for mobile app
2. **Riverpod Providers**: Implement account state management and caching
3. **UI Screens**: Build account creation/editing forms with Material 3 design
4. **API Integration**: Replace static data with real GraphQL calls
5. **Error Handling**: Implement loading states and user-friendly error messages
6. **Form Validation**: Account type selection and input validation

### Transaction Management Flow (MAJOR PROGRESS - 80% COMPLETE)
1. **✅ Database Schema**: Leverages existing `journalEntries` and `journalLines` tables for double-entry accounting
2. **✅ Service Layer**: Complete transaction service with Zod validation, double-entry logic, and balance updates
3. **✅ GraphQL Schema**: Transaction types, enums, input/output types with proper transformations
4. **✅ Resolvers**: Full CRUD operations (transactions, transaction, createTransaction, updateTransaction, deleteTransaction)
5. **✅ Double-Entry Validation**: Ensures debits equal credits with comprehensive error handling
6. **✅ Mobile Data Layer**: Complete Freezed models, GraphQL service, and Riverpod providers
7. **✅ Main UI Screen**: Advanced transaction list with date grouping, filtering, search, and pull-to-refresh
8. **🔄 Form Screens**: Add/edit transaction forms and detail view (remaining work)

### Authentication Flow
1. User enters credentials or selects OAuth provider
2. Client validates input and sends GraphQL mutation to API
3. API validates credentials (email/password hash verification or OAuth callback processing)
4. API generates JWT token with user data
5. Client stores token securely and updates authentication state
6. Subsequent requests include token for protected operations
7. Comprehensive error handling at all levels with user-friendly messages

### Transaction Recording Flow (IMPLEMENTED)
1. User enters transaction details in mobile app
2. Client validates and sends mutation to API
3. API processes using double-entry accounting principles
4. API creates journal entry and corresponding journal lines (minimum two for double-entry)
5. API updates account balances and returns response
6. Client updates local state and cache

### Code Quality Standards (V2 Ruleset Compliance)
- **Build Success**: `pnpm build` passes without errors (100% success)
- **Zero Linting Issues**: 0 errors/warnings with Biome.js (down from 35 errors + 117 warnings)
- **Type Safety**: Eliminated ALL `any` types from entire codebase with strategic `unknown` usage
- **Flutter Analysis Clean**: Zero warnings/infos - complete mobile code quality compliance
- **V2 Ruleset Compliance**: 3/4 major subtasks completed with significant improvements
  - **Constants Centralization**: All magic strings/numbers in `constants.ts` with UPPER_SNAKE_CASE
  - **Function Length**: All functions under 25 lines with single responsibilities
  - **Type Safety**: Strategic use of `unknown` with controlled type assertions
  - **Modern Patterns**: Current Flutter API usage, proper code generation optimization
- **Modern Patterns**: ESM modules, `node:` protocol imports, template literals, proper error handling
- **Testing Excellence**: Perfect test isolation with transaction-based rollback, dynamic test data generation
- **Service Layer Pattern**: Modular services with proper validation, error handling, and authentication
- **GraphQL Code-First**: API generates schema, mobile consumes via operations
- **Soft Delete Strategy**: Archive records instead of hard deletion for data integrity

## V2 Ruleset Compliance Patterns (NEW)

### Constants Centralization Pattern
```typescript
// apps/api/src/config/constants.ts - Single source of truth for all constants
export const API_CONSTANTS = {
  // Pagination
  DEFAULT_TRANSACTION_LIMIT: 50,
  DEFAULT_PAGINATION_OFFSET: 0,
  MAX_TRANSACTION_LIMIT: 100,

  // JWT Configuration
  JWT_EXPIRES_IN: '7d',
  JWT_ALGORITHM: 'HS256',

  // Validation Limits
  MAX_ACCOUNT_NAME_LENGTH: 255,
  MAX_CATEGORY_NAME_LENGTH: 100,
  MAX_TRANSACTION_DESCRIPTION_LENGTH: 500,

  // Database
  TRANSACTION_ISOLATION_LEVEL: 'read committed',
  CONNECTION_TIMEOUT_MS: 30000,
} as const;

// Usage pattern - import and use constants
import { API_CONSTANTS } from '../config/constants.js';

// ✅ Correct: Use named constants
const limit = API_CONSTANTS.DEFAULT_TRANSACTION_LIMIT;

// ❌ Avoid: Magic numbers
const limit = 50;
```

### Function Length & SRP Pattern
```typescript
// Before: Long function with multiple responsibilities (107 lines)
export async function createTransaction(userId: string, input: CreateTransactionInput) {
  // ... 107 lines of mixed concerns
}

// After: Short orchestration function with single responsibility (25 lines)
export async function createTransaction(userId: string, input: CreateTransactionInput): Promise<TransactionResponse> {
  try {
    logger.info('Creating transaction for user:', { userId, input });
    const database = ensureDb();

    return await database.transaction(async tx => {
      const journalEntry = await createJournalEntry(tx, userId, input);

      let journalLinesData: JournalLineData[];
      if (input.accountId && input.type) {
        journalLinesData = buildSimpleJournalLines(journalEntry.id, input);
      } else if (input.journalLines) {
        journalLinesData = buildManualJournalLines(journalEntry.id, input);
        validateDoubleEntry(journalLinesData);
      } else {
        throw new ValidationError('Either simple transaction fields or journal lines must be provided');
      }

      await tx.insert(journalLines).values(journalLinesData);
      await updateAccountBalances(tx, journalLinesData);

      return await getTransactionById(journalEntry.id, userId, tx);
    });
  } catch (error) {
    logger.error('Error creating transaction:', error);
    if (error instanceof AppError) throw error;
    throw new DatabaseError('Error creating transaction');
  }
}

// Helper functions with single responsibilities
async function createJournalEntry(tx: DatabaseTransaction, userId: string, input: CreateTransactionInput) {
  // Single responsibility: Create journal entry
}

function buildSimpleJournalLines(journalEntryId: string, input: CreateTransactionInput): JournalLineData[] {
  // Single responsibility: Build journal lines for simple transactions
}

function validateDoubleEntry(journalLinesData: JournalLineData[]): void {
  // Single responsibility: Validate double-entry accounting rules
}
```

### Type Safety Enforcement Pattern
```typescript
// Strategic use of unknown with controlled type assertions
type DatabaseTransaction = unknown; // TODO: Replace with proper Drizzle type when available

async function createJournalEntry(tx: DatabaseTransaction, userId: string, input: CreateTransactionInput) {
  const txTyped = tx as any; // Type assertion with clear comment explaining necessity
  const [journalEntry] = await txTyped
    .insert(journalEntries)
    .values({
      userId,
      description: input.description,
      // ... other fields
    })
    .returning();

  return journalEntry;
}

// GraphQL transformations with proper interfaces
interface TransactionInputTransform {
  description?: string;
  amount?: number;
  date?: string | Date;
  notes?: string;
  accountId?: string;
  categoryId?: string;
  type?: string;
  status?: string;
  isRecurring?: boolean;
  recurringPattern?: unknown;
  journalLines?: unknown[];
}

const transformTransactionInput = (
  input: GraphQLCreateTransactionInput | GraphQLUpdateTransactionInput
): TransactionInputTransform => {
  const transformed: TransactionInputTransform = { ...input };

  // Convert null values to undefined for Zod validation
  for (const key of Object.keys(transformed)) {
    if (transformed[key as keyof TransactionInputTransform] === null) {
      transformed[key as keyof TransactionInputTransform] = undefined;
    }
  }

  return transformed;
};
```

### Mobile Type Safety Pattern
```dart
// Replace inappropriate dynamic usage with proper types
class TransactionTypeConverter {
  // Before: dynamic parameter
  static TransactionType fromJson(dynamic value) { ... }

  // After: Object? with proper null handling
  static TransactionType fromJson(Object? value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'income': return TransactionType.income;
        case 'expense': return TransactionType.expense;
        case 'transfer': return TransactionType.transfer;
      }
    }
    throw ArgumentError('Unknown TransactionType: $value');
  }
}

// Optimize code generation usage
factory Category.fromJson(Map<String, dynamic> json) {
  // Preprocess to provide defaults for missing fields
  final processedJson = Map<String, dynamic>.from(json);
  processedJson['isDefault'] ??= false;
  processedJson['isSystem'] ??= false;
  processedJson['isArchived'] ??= false;
  processedJson['displayOrder'] ??= 0;
  processedJson['createdAt'] ??= DateTime.now().toIso8601String();
  processedJson['updatedAt'] ??= DateTime.now().toIso8601String();

  // Use generated function for consistency
  return _$CategoryFromJson(processedJson);
}

// Modern Flutter API usage
Container(
  // Before: Deprecated method
  color: AppColors.primary.withOpacity(0.1),

  // After: Modern API
  color: AppColors.primary.withValues(alpha: 0.1),
)
```

### Code Quality Verification Pattern
```bash
# API build verification
cd apps/api && pnpm build

# Mobile analysis verification
cd apps/mobile && flutter analyze

# Expected results:
# API: "Build completed successfully"
# Mobile: "No issues found!"
```

## Transaction Management Patterns (NEW)

### Double-Entry Accounting Implementation
```typescript
// Backend: Transaction validation with double-entry principles
const validateDoubleEntry = (journalLines: JournalLineInput[]): void => {
  const totalDebits = journalLines
    .filter(line => line.type === 'DEBIT')
    .reduce((sum, line) => sum + line.amount, 0);
  
  const totalCredits = journalLines
    .filter(line => line.type === 'CREDIT')
    .reduce((sum, line) => sum + line.amount, 0);
  
  if (Math.abs(totalDebits - totalCredits) > 0.01) {
    throw new Error('Transaction must balance: debits must equal credits');
  }
};

// Automatic journal line creation for simple transactions
const createJournalLinesForTransaction = (
  type: TransactionType,
  amount: number,
  accountId: string,
  categoryId?: string
): JournalLineInput[] => {
  switch (type) {
    case 'INCOME':
      return [
        { accountId, amount, type: 'DEBIT', categoryId },
        { accountId: 'income-account', amount, type: 'CREDIT', categoryId }
      ];
    case 'EXPENSE':
      return [
        { accountId: 'expense-account', amount, type: 'DEBIT', categoryId },
        { accountId, amount, type: 'CREDIT', categoryId }
      ];
    case 'TRANSFER':
      // Requires two accounts in input
      return journalLines; // Pass through for manual specification
  }
};
```

## Mobile UI Color Scheme Patterns (NEW)

### Material Design 3 Implementation
```dart
// Complete ColorScheme with branded colors
class AppTheme {
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: const ColorScheme.light(
      primary: AppColors.primary,           // #01A2A1 (Teal)
      secondary: AppColors.secondary,       // #FC2F20 (Coral Red)
      tertiary: AppColors.tertiary,         // #FF8F00 (Orange)
      error: AppColors.error,               // #BA1A1A (Red)
      surface: AppColors.surface,           // #FAFDFDF (Light)
      // ... complete color system
    ),
  );
}
```

### Financial Color Semantics
```dart
// Semantic colors for financial data
Widget _buildAccountItem(String name, double amount, Color color) {
  final isNegative = amount < 0;
  return ListTile(
    trailing: Text(
      '\$${amount.abs().toStringAsFixed(2)}',
      style: TextStyle(
        color: isNegative ? AppColors.error : AppColors.success,
        fontWeight: AppTypography.fontWeightSemiBold,
      ),
    ),
  );
}

// Budget progress indicators
LinearProgressIndicator(
  value: percentage,
  backgroundColor: AppColors.surfaceVariant,
  valueColor: AlwaysStoppedAnimation<Color>(
    isOverBudget ? AppColors.error : color,
  ),
)
```

### Splash Screen Design Pattern
```dart
// Beautiful gradient background with elevated container
Container(
  decoration: BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppColors.splashBackground,  // #01A2A1
        AppColors.primaryDark,       // #007372
      ],
    ),
  ),
  child: Center(
    child: Container(
      padding: const EdgeInsets.all(AppSpacing.xl),
      decoration: BoxDecoration(
        color: AppColors.onPrimary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.xxl),
        border: Border.all(
          color: AppColors.onPrimary.withValues(alpha: 0.2),
          width: 2,
        ),
      ),
      child: const Icon(
        Icons.account_balance_wallet_rounded,
        size: 80,
        color: AppColors.onPrimary,
      ),
    ),
  ),
)
```

### Design Token Architecture
```dart
// Centralized color system in design_tokens.dart
class AppColors {
  // Primary colors (Teal #01a2a1)
  static const primary = Color(0xFF01A2A1);
  static const primaryLight = Color(0xFF4DD4D3);
  static const primaryDark = Color(0xFF007372);
  
  // Secondary colors (Coral Red #fc2f20)
  static const secondary = Color(0xFFFC2F20);
  static const secondaryLight = Color(0xFFFF6B5B);
  static const secondaryDark = Color(0xFFC30000);
  
  // Financial semantic colors
  static const success = Color(0xFF2E7D32);  // Green for positive
  static const error = Color(0xFFBA1A1A);    // Red for negative
  static const warning = Color(0xFFED6C02);  // Orange for alerts
  static const info = Color(0xFF0288D1);     // Blue for neutral
}

// Usage pattern - always use semantic tokens
Container(
  color: AppColors.primary,              // ✅ Correct
  color: Color(0xFF01A2A1),             // ❌ Avoid hardcoded
)
```

### Component Theming Pattern
```dart
// Consistent component theming using design tokens
elevatedButtonTheme: ElevatedButtonThemeData(
  style: ElevatedButton.styleFrom(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.onPrimary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppBorderRadius.md),
    ),
  ),
),

cardTheme: CardTheme(
  color: AppColors.surface,
  surfaceTintColor: AppColors.surfaceTint,
  elevation: 1,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(AppBorderRadius.lg),
  ),
),
```

## Account Creation & Validation Patterns (NEW)

### Zod Validation Schema for Nullable Optional Fields
```typescript
// Correct pattern for fields that can be null or undefined
export const createAccountInputSchema = z.object({
  name: z.string().min(1, 'Account name is required').max(255, 'Account name must be less than 255 characters'),
  type: accountTypeSchema,
  currency: z.string().length(3, 'Currency must be a 3-letter code').default('USD'),
  initialBalance: z.number().default(0),
  // ✅ Correct: Handle both null and undefined values
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').nullable().optional(),
  icon: z.string().max(50, 'Icon must be less than 50 characters').nullable().optional(),
  color: z.string().max(20, 'Color must be less than 20 characters').nullable().optional(),
  includeInNetWorth: z.boolean().default(true),
  displayOrder: z.number().int().min(0).default(0),
});

// ❌ Incorrect: Only handles undefined, not null
notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
```

### Mobile Environment Configuration Pattern
```dart
// Environment-aware API configuration
class Environment {
  // API URL configuration with fallback defaults
  static const String apiUrl = String.fromEnvironment(
    'API_URL',
    defaultValue: 'http://localhost:3000/graphql', // Development default
  );

  // Environment type for debugging and logging
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  // Helper methods for environment detection
  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';

  // Debug information
  static void printEnvironmentInfo() {
    assert(() {
      print('Environment: $environment');
      print('API URL: $apiUrl');
      return true;
    }());
  }
}

// Build script usage
flutter run --dart-define=API_URL="http://localhost:3000/graphql" \
  --dart-define=ENVIRONMENT="development"
```

### Account Type Configuration Pattern
```dart
// Automatic icon and color assignment from account type
class AddAccountScreen extends ConsumerStatefulWidget {
  // Account type configurations with icons and descriptions
  static const Map<AccountType, Map<String, dynamic>> _accountTypeConfig = {
    AccountType.checking: {
      'icon': Icons.account_balance,
      'label': 'Checking Account',
      'description': 'For everyday spending and transactions',
      'color': Colors.blue,
    },
    AccountType.savings: {
      'icon': Icons.savings,
      'label': 'Savings Account',
      'description': 'For saving money and earning interest',
      'color': Colors.green,
    },
    // ... other account types
  };

  Future<void> _saveAccount() async {
    // Get icon and color from account type configuration
    final accountConfig = _accountTypeConfig[_selectedAccountType]!;
    final iconName = accountConfig['icon'].toString().split('.').last;
    final colorHex = '#${(accountConfig['color'] as Color).value.toRadixString(16).substring(2).toUpperCase()}';

    final input = CreateAccountInput(
      name: _nameController.text.trim(),
      type: _selectedAccountType,
      currency: _selectedCurrency,
      initialBalance: initialBalance,
      // ✅ Proper null handling for optional fields
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      icon: iconName,
      color: colorHex,
      includeInNetWorth: _includeInNetWorth,
      displayOrder: 0,
    );
  }
}
```

### GraphQL Debugging Pattern
```bash
# Comprehensive testing approach for GraphQL validation issues

# 1. Backend isolation testing
curl -X POST http://localhost:3000/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "query": "mutation CreateAccount($input: CreateAccountInput!) { createAccount(input: $input) { id name type } }",
    "variables": {
      "input": {
        "name": "Test Account",
        "type": "checking",
        "currency": "USD",
        "initialBalance": 100.00,
        "notes": null,
        "icon": null,
        "color": null,
        "includeInNetWorth": true,
        "displayOrder": 0
      }
    }
  }'

# 2. Mobile configuration verification
flutter run --dart-define=API_URL="http://localhost:3000/graphql" \
  --dart-define=ENVIRONMENT="development"

# 3. API server health check
curl -X POST http://localhost:3000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ hello }"}'

# 4. Test script pattern for comprehensive validation
#!/bin/bash
API_URL="http://localhost:3000/graphql"
TIMESTAMP=$(date +%s)
EMAIL="test${TIMESTAMP}@example.com"

# Register user and extract token
REGISTER_RESPONSE=$(curl -s -X POST $API_URL \
  -H "Content-Type: application/json" \
  -d '{"query": "mutation Register($input: RegisterInput!) { register(input: $input) { token } }", "variables": {"input": {"email": "'$EMAIL'", "password": "TestPass123!", "firstName": "Test", "lastName": "User"}}}')

TOKEN=$(echo $REGISTER_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

# Test account creation with extracted token
ACCOUNT_RESPONSE=$(curl -s -X POST $API_URL \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"query": "mutation CreateAccount($input: CreateAccountInput!) { createAccount(input: $input) { id name } }", "variables": {"input": {"name": "Test Account", "type": "checking", "currency": "USD", "initialBalance": 100.00, "notes": null, "icon": null, "color": null, "includeInNetWorth": true, "displayOrder": 0}}}')

echo "Account creation result: $ACCOUNT_RESPONSE"
```

### Development Workflow for GraphQL Issues
1. **Error Analysis**: Identify GraphQL validation error from mobile app logs
2. **Backend Isolation**: Test backend functionality independently with curl/scripts
3. **Schema Investigation**: Verify Zod schemas handle all expected input types (null, undefined)
4. **Mobile Configuration**: Ensure proper environment configuration for development
5. **Comprehensive Testing**: Create test scripts to verify fixes work end-to-end
6. **Integration Verification**: Test complete mobile-to-backend flow

### Error Handling Pattern for Validation Issues
```typescript
// Backend: Comprehensive error formatting
const formatZodError = (error: ZodError): string => {
  return error.errors
    .map(err => `${err.path.join('.')}: ${err.message}`)
    .join(', ');
};

// GraphQL resolver error handling
try {
  const validatedInput = createAccountInputSchema.parse(input);
  const account = await accountsService.createAccount(userId, validatedInput);
  return account;
} catch (error) {
  if (error instanceof ZodError) {
    throw new Error(`Validation failed: ${formatZodError(error)}`);
  }
  throw error;
}
```

```dart
// Mobile: Proper error display
try {
  final account = await ref.read(accountsProvider.notifier).createAccount(input);
  if (account != null && mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Account "${account.name}" created successfully'),
        backgroundColor: AppColors.success,
      ),
    );
    context.pop();
  }
} catch (e) {
  if (mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Error creating account: $e'),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
```

### **Mobile Navigation Architecture**

#### **Unified Screen Reusability Pattern**
```dart
// Enhanced TransactionsScreen with configurable behavior
class TransactionsScreen extends ConsumerWidget {
  final TransactionFilter? initialFilter;
  final String? customTitle;
  final bool showAddButton;
  
  const TransactionsScreen({
    super.key,
    this.initialFilter,
    this.customTitle,
    this.showAddButton = true,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text(customTitle ?? 'Transactions'),
        actions: showAddButton ? [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/transactions/add'),
          ),
        ] : null,
      ),
      body: TransactionsList(initialFilter: initialFilter),
    );
  }
}
```

#### **Router Configuration with Query Parameters**
```dart
// Account-specific transaction routes
GoRoute(
  path: '/account/:accountId/transactions',
  builder: (context, state) {
    final accountId = state.pathParameters['accountId']!;
    final accountName = state.uri.queryParameters['name'] ?? 'Account';
    
    return TransactionsScreen(
      initialFilter: TransactionFilter(accountId: accountId),
      customTitle: '$accountName Transactions',
      showAddButton: false, // Hide add button in filtered views
    );
  },
),

// Category-specific transaction routes
GoRoute(
  path: '/category/:categoryId/transactions',
  builder: (context, state) {
    final categoryId = state.pathParameters['categoryId']!;
    final categoryName = state.uri.queryParameters['name'] ?? 'Category';
    
    return TransactionsScreen(
      initialFilter: TransactionFilter(categoryId: categoryId),
      customTitle: '$categoryName Transactions',
      showAddButton: false,
    );
  },
),
```

#### **Clickable Navigation Pattern**
```dart
// Account navigation with encoded parameters
InkWell(
  onTap: () => context.push(
    '/account/${account.id}/transactions?name=${Uri.encodeComponent(account.name)}'
  ),
  child: AccountCard(account: account),
)

// Category navigation with hierarchy support
InkWell(
  onTap: () => context.push(
    '/category/${category.id}/transactions?name=${Uri.encodeComponent(category.name)}'
  ),
  child: CategoryCard(category: category, isSubcategory: depth > 0),
)
```

#### **Category Hierarchy Implementation**
```dart
// Category tree structure
class CategoryNode {
  final Category category;
  final List<CategoryNode> children;
  
  const CategoryNode({
    required this.category,
    this.children = const [],
  });
}

// Hierarchy building algorithm
List<CategoryNode> _buildCategoryHierarchy(List<Category> categories) {
  final Map<String, CategoryNode> nodeMap = {};
  final List<CategoryNode> rootNodes = [];
  
  // Create nodes for all categories
  for (final category in categories) {
    nodeMap[category.id] = CategoryNode(category: category);
  }
  
  // Build parent-child relationships
  for (final category in categories) {
    final node = nodeMap[category.id]!;
    
    if (category.parentId != null && nodeMap.containsKey(category.parentId)) {
      final parentNode = nodeMap[category.parentId]!;
      parentNode.children.add(node);
    } else {
      rootNodes.add(node);
    }
  }
  
  return rootNodes;
}

// Visual hierarchy with indentation
Widget _buildCategoryItem(CategoryNode node, {int depth = 0}) {
  return Padding(
    padding: EdgeInsets.only(left: depth * 16.0),
    child: InkWell(
      onTap: () => _navigateToTransactions(node.category),
      child: CategoryCard(
        category: node.category,
        isSubcategory: depth > 0,
        children: node.children.map((child) => 
          _buildCategoryItem(child, depth: depth + 1)
        ).toList(),
      ),
    ),
  );
}
```

### **Provider Architecture Patterns**

#### **Dual Provider Pattern for Complex State**
```dart
// State-based provider for complex operations
@riverpod
class Accounts extends _$Accounts {
  late AccountsService _accountsService;
  final StreamController<AccountsEvent> _controller = StreamController<AccountsEvent>.broadcast();
  
  @override
  AccountsState build() {
    final client = ref.read(graphQLClientProvider);
    _accountsService = AccountsService(client: client);
    return const AccountsState.initial();
  }
  
  Future<void> loadAccounts() async {
    // Complex state management logic
  }
  
  Future<void> refresh() async {
    await loadAccounts();
  }
}

// Simple async provider for UI consumption
@riverpod
Future<List<Account>> accountsList(Ref ref) async {
  final client = ref.read(graphQLClientProvider);
  final accountsService = AccountsService(client: client);
  
  try {
    final accounts = await accountsService.getAccounts();
    return accounts;
  } catch (e) {
    throw Exception('Failed to load accounts: $e');
  }
}
```

#### **Provider Usage Pattern Consistency (CRITICAL)**
```dart
// ✅ CORRECT: Use async providers for UI screens
final accountsAsync = ref.watch(accountsListProvider);
accountsAsync.when(
  data: (accounts) => _buildAccountsList(accounts),
  loading: () => const CircularProgressIndicator(),
  error: (error, stack) => Text('Error: $error'),
);

// ❌ INCORRECT: Using state provider without loading
final accountsState = ref.watch(accountsProvider); // Stays in 'initial' state
switch (accountsState.status) {
  case AccountsStatus.initial:
    return const Text('Loading accounts...'); // Stuck here forever
  // ... other cases never reached
}
```

#### **Screen Provider Pattern Guidelines**
```dart
// UI Screens should use async providers that auto-load:
// - accountsListProvider (for accounts)
// - categoryNotifierProvider (for categories) 
// - transactionsProvider() (for transactions)

// State providers are for complex operations:
// - accountsProvider (for CRUD operations)
// - Use .notifier to call methods like loadAccounts(), createAccount()

// Example: Add Transaction Screen (FIXED)
class AddTransactionScreen extends ConsumerStatefulWidget {
  @override
  Widget build(BuildContext context) {
    // ✅ Use async provider for UI display
    final accountsAsync = ref.watch(accountsListProvider);
    final categoriesAsync = ref.watch(categoryNotifierProvider);
    
    return accountsAsync.when(
      data: (accounts) => _buildAccountDropdown(accounts),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
  
  Future<void> _saveTransaction() async {
    // ✅ Use state provider for operations
    await ref.read(transactionsProvider().notifier).addTransaction(input);
  }
}
```

#### **Provider Pattern Anti-Patterns (AVOID)**
```dart
// ❌ DON'T: Mix provider types incorrectly
final accountsState = ref.watch(accountsProvider); // State provider
// Then expect it to have data without calling loadAccounts()

// ❌ DON'T: Use complex state handling for simple UI
Widget _buildAccountSection(AccountsState accountsState) {
  switch (accountsState.status) {
    case AccountsStatus.loading: return CircularProgressIndicator();
    case AccountsStatus.error: return Text('Error: ${accountsState.errorMessage}');
    case AccountsStatus.loaded: return _buildAccountDropdown(accountsState.accounts);
    case AccountsStatus.initial: return Text('Loading accounts...'); // Stuck here
  }
}

// ✅ DO: Use async provider with .when() pattern
accountsAsync.when(
  data: (accounts) => _buildAccountDropdown(accounts),
  loading: () => const CircularProgressIndicator(),
  error: (error, stack) => Text('Error: $error'),
)
```

#### **Provider Architecture Decision Tree**
```dart
// When to use which provider:

// For UI Display (Reading Data):
// - Use async providers: accountsListProvider, categoryNotifierProvider
// - Pattern: ref.watch(provider).when(data: ..., loading: ..., error: ...)

// For Operations (Writing Data):
// - Use state providers: accountsProvider.notifier, transactionsProvider().notifier  
// - Pattern: ref.read(provider.notifier).methodName()

// For Complex State Management:
// - Use state providers with manual state handling
// - Pattern: ref.watch(provider) then handle state.status manually
```

### **Floating Action Button Navigation Patterns (NEW)**

#### **Strategic FAB Placement for Primary Actions**
```dart
// Home Screen - Primary action is transaction creation
class HomeScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('BudApp')),
      body: _buildHomeContent(context, ref),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/add-transaction'); // Primary user action
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}

// Accounts Screen - FAB for transactions, AppBar for accounts
class AccountsScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.push('/accounts/add'); // Secondary action
            },
          ),
        ],
      ),
      body: _buildAccountsList(context, ref),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/add-transaction'); // Primary action
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
```

#### **Navigation Hierarchy Pattern**
```dart
// Primary Actions (Floating Action Buttons)
// - Transaction creation: Most frequent user action
// - Available on: Home screen, Accounts screen
// - Route: '/add-transaction'

// Secondary Actions (AppBar Buttons)
// - Account creation: Less frequent, contextual to accounts screen
// - Category creation: Less frequent, contextual to categories screen
// - Available on: Respective screens only

// Navigation Flow Optimization
class NavigationPatterns {
  // Primary action - always accessible via FAB
  static void addTransaction(BuildContext context) {
    context.push('/add-transaction');
  }
  
  // Secondary actions - contextual via AppBar
  static void addAccount(BuildContext context) {
    context.push('/accounts/add');
  }
  
  static void addCategory(BuildContext context) {
    context.push('/categories/add');
  }
}
```

#### **User Experience Consistency**
```dart
// Consistent FAB behavior across screens
mixin TransactionCreationMixin {
  Widget buildTransactionFAB(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => context.push('/add-transaction'),
      child: const Icon(Icons.add),
      tooltip: 'Add Transaction',
    );
  }
}

// Usage in multiple screens
class HomeScreen extends ConsumerWidget with TransactionCreationMixin {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      // ... other properties
      floatingActionButton: buildTransactionFAB(context),
    );
  }
}

class AccountsScreen extends ConsumerWidget with TransactionCreationMixin {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      // ... other properties
      floatingActionButton: buildTransactionFAB(context),
    );
  }
}
```

#### **Action Priority Guidelines**
```dart
// Action frequency analysis for UX decisions
enum ActionFrequency {
  primary,   // Daily use - FAB placement
  secondary, // Weekly use - AppBar placement  
  tertiary,  // Monthly use - Menu/settings placement
}

class ActionPriorityMap {
  static const Map<String, ActionFrequency> actions = {
    'add_transaction': ActionFrequency.primary,    // FAB
    'add_account': ActionFrequency.secondary,      // AppBar
    'add_category': ActionFrequency.secondary,     // AppBar
    'view_reports': ActionFrequency.tertiary,      // Navigation
    'export_data': ActionFrequency.tertiary,       // Menu
  };
}
```

### **Flutter Modern Practices**

#### **Deprecation Handling**
```dart
// Modern opacity handling (Flutter 3.27+)
Container(
  decoration: BoxDecoration(
    color: AppColors.primary.withValues(alpha: 0.1), // New approach
    // color: AppColors.primary.withOpacity(0.1),    // Deprecated
  ),
)

// Modern color handling
Text(
  'Balance',
  style: TextStyle(
    color: Colors.white.withValues(alpha: 0.9), // New approach
    // color: Colors.white.withOpacity(0.9),       // Deprecated
  ),
)
```

#### **Provider Refresh Best Practices**
```dart
// Preferred: Invalidate provider to trigger rebuild
onPressed: () => ref.invalidate(accountsListProvider),

// Alternative: Refresh future (but handle unused result)
onPressed: () async {
  await ref.refresh(accountsListProvider.future);
},

// Avoid: Direct refresh without handling result
// onPressed: () => ref.refresh(accountsListProvider.future), // Causes warning
```

#### **Import Organization**
```dart
// Standard import order
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// App imports
import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/providers/accounts_provider.dart';
import 'package:budapp/models/account.dart';
import 'package:budapp/utils/account_utils.dart'; // For extensions
```

### **GraphQL Cache Management Patterns (NEW)**

#### **Cache Configuration for Complex Nested Data**
```dart
// Disable normalization to prevent PartialDataException with complex nested structures
return GraphQLClient(
  link: link,
  cache: GraphQLCache(
    store: InMemoryStore(),
    // Disable normalization to avoid PartialDataException issues
    // This is safer for complex nested data structures like transactions with journal lines
    dataIdFromObject: (object) => null,
  ),
  defaultPolicies: DefaultPolicies(
    query: Policies(
      fetch: FetchPolicy.cacheAndNetwork,
      error: ErrorPolicy.all,
    ),
    mutate: Policies(
      fetch: FetchPolicy.networkOnly,
      error: ErrorPolicy.all,
    ),
  ),
);
```

#### **Error Recovery Pattern for Cache Issues**
```dart
// Automatic PartialDataException detection and recovery
@riverpod
class FilteredTransactions extends _$FilteredTransactions {
  @override
  Future<List<Transaction>> build(TransactionFilter filter) async {
    final service = ref.read(transactionsServiceProvider);
    try {
      return await service.getTransactions(filter: filter);
    } catch (e) {
      // If we get a cache-related error, clear cache and retry
      if (e.toString().contains('PartialDataException')) {
        final client = ref.read(graphQLClientProvider);
        client.cache.store.reset();
        // Retry after cache clear
        return await service.getTransactions(filter: filter);
      }
      rethrow;
    }
  }
}
```

#### **Service-Level Cache Error Handling**
```dart
// Enhanced error handling in TransactionsService
Future<List<Transaction>> getTransactions({
  TransactionFilter? filter,
  int limit = 50,
  int offset = 0,
}) async {
  try {
    final result = await _client.query(
      QueryOptions(
        document: gql(_getTransactionsQuery),
        variables: {
          'filter': filter?.toJson(),
          'limit': limit,
          'offset': offset,
        },
        fetchPolicy: FetchPolicy.networkOnly, // Force network to avoid cache issues
        errorPolicy: ErrorPolicy.all,
      ),
    );

    if (result.hasException) {
      // Check for PartialDataException specifically
      final exception = result.exception;
      if (exception.toString().contains('PartialDataException')) {
        // Retry with network-only policy
        final retryResult = await _client.query(/* ... retry logic ... */);
        // Handle retry result
      }
      throw Exception('Failed to fetch transactions: ${result.exception}');
    }
    // Process successful result
  } catch (e) {
    throw Exception('Failed to fetch transactions: $e');
  }
}
```

#### **Model Deserialization Patterns for Partial Data**
```dart
// Graceful handling of partial data in model deserialization
factory Category.fromJson(Map<String, dynamic> json) {
  // Handle missing fields gracefully for partial category data (e.g., from transaction queries)
  return Category(
    id: json['id'] as String,
    name: json['name'] as String,
    type: CategoryTypeConverter.fromJson(json['type'] as String),
    parentId: json['parentId'] as String?,
    icon: json['icon'] as String?,
    color: json['color'] as String?,
    // Provide safe defaults for missing required fields
    isDefault: json['isDefault'] as bool? ?? false,
    isSystem: json['isSystem'] as bool? ?? false,
    isArchived: json['isArchived'] as bool? ?? false,
    displayOrder: json['displayOrder'] as int? ?? 0,
    // Handle optional date fields with fallbacks
    createdAt: json['createdAt'] != null ? _parseDateTime(json['createdAt']) : DateTime.now(),
    updatedAt: json['updatedAt'] != null ? _parseDateTime(json['updatedAt']) : DateTime.now(),
    // Handle optional nested objects
    children: json['children'] != null
        ? (json['children'] as List).map((e) => Category.fromJson(e as Map<String, dynamic>)).toList()
        : null,
    parent: json['parent'] != null
        ? Category.fromJson(json['parent'] as Map<String, dynamic>)
        : null,
  );
}
```

#### **Cache Strategy Guidelines**
```dart
// When to use different cache strategies:

// 1. Complex Nested Data (Transactions with Journal Lines)
// - Use dataIdFromObject: null to disable normalization
// - Use FetchPolicy.networkOnly for queries to avoid cache conflicts
// - Implement error recovery with cache clearing

// 2. Simple Data (Accounts, Categories)
// - Can use normal cache normalization
// - Use FetchPolicy.cacheAndNetwork for better performance
// - Standard error handling

// 3. Mutations
// - Always use FetchPolicy.networkOnly
// - Clear relevant cache entries after successful mutations
// - Update local state optimistically when appropriate
```

### **Flutter Layout Constraint Patterns (ESTABLISHED)**

#### **Dropdown Menu Item Layout Best Practices**
```dart
// ✅ CORRECT: Proper layout for dropdown menu items
DropdownMenuItem(
  value: item,
  child: Row(
    mainAxisSize: MainAxisSize.min,  // Prevents unbounded constraints
    children: [
      Icon(item.icon),
      const SizedBox(width: AppSpacing.sm),
      Flexible(  // Allows flexible sizing without forcing expansion
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.name,
              overflow: TextOverflow.ellipsis,  // Handles text overflow
            ),
            Text(item.subtitle),
          ],
        ),
      ),
    ],
  ),
)

// ❌ INCORRECT: Causes layout constraint violations
DropdownMenuItem(
  value: item,
  child: Row(  // Missing mainAxisSize configuration
    children: [
      Icon(item.icon),
      const SizedBox(width: AppSpacing.sm),
      Expanded(  // Causes unbounded width constraint error
        child: Column(
          children: [
            Text(item.name),  // Can overflow without ellipsis
            Text(item.subtitle),
          ],
        ),
      ),
    ],
  ),
)
```

#### **Layout Constraint Resolution Patterns**
```dart
// Pattern 1: Flexible vs Expanded usage
// Use Flexible when content should adapt to available space
Flexible(
  child: Text(
    longText,
    overflow: TextOverflow.ellipsis,
  ),
)

// Use Expanded only when you want to force expansion to fill space
Expanded(
  child: Container(
    color: Colors.blue,
    child: Text('Fills remaining space'),
  ),
)

// Pattern 2: Row mainAxisSize configuration
Row(
  mainAxisSize: MainAxisSize.min,  // Shrink-wrap content
  children: [...],
)

Row(
  mainAxisSize: MainAxisSize.max,  // Fill available width (default)
  children: [...],
)

// Pattern 3: Text overflow handling in constrained layouts
Text(
  potentiallyLongText,
  overflow: TextOverflow.ellipsis,
  maxLines: 1,
)
```

#### **Common Layout Constraint Errors and Solutions**
```dart
// Error: "RenderFlex children have non-zero flex but incoming width constraints are unbounded"
// Cause: Using Expanded in a context where parent doesn't provide finite width

// ❌ Problematic pattern
ListView(
  children: [
    Row(
      children: [
        Expanded(child: Text('This causes error')),  // ListView provides unbounded width
      ],
    ),
  ],
)

// ✅ Fixed pattern
ListView(
  children: [
    Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(child: Text('This works correctly')),
      ],
    ),
  ],
)

// Error: Text overflow in dropdown items
// ✅ Solution: Always handle text overflow in constrained contexts
Text(
  account.name,
  overflow: TextOverflow.ellipsis,
  style: const TextStyle(fontWeight: FontWeight.w500),
)
```

#### **Dropdown Menu Design Patterns**
```dart
// Standard dropdown item with icon and text
Widget buildDropdownItem<T>(T item, String Function(T) getName, IconData Function(T) getIcon) {
  return DropdownMenuItem<T>(
    value: item,
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(getIcon(item), size: 16),
        const SizedBox(width: AppSpacing.sm),
        Flexible(
          child: Text(
            getName(item),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    ),
  );
}

// Complex dropdown item with subtitle
Widget buildComplexDropdownItem<T>(
  T item,
  String Function(T) getName,
  String Function(T) getSubtitle,
  Widget Function(T) getLeading,
) {
  return DropdownMenuItem<T>(
    value: item,
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        getLeading(item),
        const SizedBox(width: AppSpacing.sm),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                getName(item),
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                getSubtitle(item),
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
```

#### **Layout Debugging Best Practices**
```dart
// Enable Flutter Inspector for layout debugging
// Use these debugging tools when encountering layout issues:

// 1. Add debug borders to understand constraints
Container(
  decoration: BoxDecoration(
    border: Border.all(color: Colors.red),  // Debug border
  ),
  child: problematicWidget,
)

// 2. Use debugPaintSizeEnabled for visual debugging
import 'package:flutter/rendering.dart';

void main() {
  debugPaintSizeEnabled = true;  // Shows widget boundaries
  runApp(MyApp());
}

// 3. Wrap problematic widgets with LayoutBuilder to inspect constraints
LayoutBuilder(
  builder: (context, constraints) {
    print('Available constraints: $constraints');
    return yourWidget;
  },
)
```
